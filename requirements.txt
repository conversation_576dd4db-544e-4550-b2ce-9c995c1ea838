# core/requirements.txt
fastapi==0.109.0
uvicorn[standard]==0.27.0
pydantic==2.5.3
pydantic-settings==2.1.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
httpx==0.26.0
redis==5.0.1
asyncpg==0.29.0
sqlalchemy==2.0.25
alembic==1.13.1
prometheus-client==0.19.0
opentelemetry-api==1.22.0
opentelemetry-sdk==1.22.0
opentelemetry-instrumentation-fastapi==0.43b0
python-dotenv==1.0.0
# Secret management dependencies (optional)
hvac==2.1.0  # HashiCorp Vault client
boto3==1.34.0  # AWS SDK
azure-keyvault-secrets==4.8.0  # Azure Key Vault
azure-identity==1.15.0  # Azure authentication
cryptography==42.0.0  # Encryption for local secrets
# monitor/requirements.txt
rich==13.7.0
httpx==0.26.0
# services/translation/requirements.txt
fastapi==0.109.0
uvicorn[standard]==0.27.0
pydantic==2.5.3
httpx==0.26.0
openai==1.82.0
python-multipart==0.0.6
python-dotenv==1.0.0