﻿"""
Main Application Entry Point

This module provides the main entry point for the FastAPI application
using the new modular architecture with dependency injection and plugin system.
"""

import uvicorn
from core.application.factory import create_application
from core.config.settings import create_settings


def create_app():
    """Create FastAPI application using the new modular architecture"""
    # Load settings
    settings = create_settings()
    
    # Create application using factory
    return create_application(settings)


# Create app instance
app = create_app()


if __name__ == "__main__":
    # Load settings for development server
    settings = create_settings()
    
    # Run development server
    uvicorn.run(
        "core.main:app",
        host=settings.app.host,
        port=settings.app.port,
        reload=settings.is_development(),
        workers=1 if settings.is_development() else settings.app.workers,
        log_level=settings.logging.log_level.value.lower(),
    )