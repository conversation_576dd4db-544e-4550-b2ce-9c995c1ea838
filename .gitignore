﻿# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
.venv
pip-log.txt
pip-delete-this-directory.txt
.pytest_cache/
.coverage
htmlcov/
.tox/
*.egg-info/
dist/
build/

# Environment
.env
.env.local
.env.*.local

# IDE
.vscode/
.idea/
*.swp
*.swo
.DS_Store

# Docker
docker-compose.override.yml

# SSL certificates
nginx/ssl/*.pem
nginx/ssl/*.key
nginx/ssl/*.crt

# Logs
logs/
*.log

# Database
*.db
*.sqlite
migrations/versions/*.pyc

# Monitoring
monitoring/prometheus/data/
monitoring/grafana/data/

# Terraform
terraform/.terraform/
terraform/*.tfstate
terraform/*.tfstate.backup
terraform/.terraform.lock.hcl

# Kubernetes
k8s/secrets-prod.yaml
k8s/configmap-prod.yaml

# Temp files
*.tmp
*.bak
.cache/
