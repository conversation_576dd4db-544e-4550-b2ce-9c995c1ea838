"""
Cache Implementations

This module provides concrete implementations of the ICache interface
including Redis and in-memory cache implementations.
"""

import asyncio
import time
from typing import Any, Dict, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

from core.domain.interfaces import ICache
from core.domain.exceptions import Cache<PERSON>rror


@dataclass
class CacheEntry:
    """Represents a cache entry with TTL"""
    value: Any
    expires_at: Optional[float] = None
    
    def is_expired(self) -> bool:
        """Check if the cache entry has expired"""
        if self.expires_at is None:
            return False
        return time.time() > self.expires_at


class MemoryCache(ICache):
    """In-memory cache implementation"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, CacheEntry] = {}
        self._access_order: Dict[str, float] = {}
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        async with self._lock:
            entry = self._cache.get(key)
            if entry is None:
                return None
            
            if entry.is_expired():
                del self._cache[key]
                self._access_order.pop(key, None)
                return None
            
            # Update access time for LRU
            self._access_order[key] = time.time()
            return entry.value
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache"""
        async with self._lock:
            # Calculate expiration time
            expires_at = None
            if ttl is not None:
                expires_at = time.time() + ttl
            elif self.default_ttl > 0:
                expires_at = time.time() + self.default_ttl
            
            # Create cache entry
            entry = CacheEntry(value=value, expires_at=expires_at)
            
            # Add to cache
            self._cache[key] = entry
            self._access_order[key] = time.time()
            
            # Evict if over max size
            if len(self._cache) > self.max_size:
                await self._evict_lru()
    
    async def delete(self, key: str) -> None:
        """Delete value from cache"""
        async with self._lock:
            self._cache.pop(key, None)
            self._access_order.pop(key, None)
    
    async def exists(self, key: str) -> bool:
        """Check if key exists"""
        entry = self._cache.get(key)
        if entry is None:
            return False
        
        if entry.is_expired():
            async with self._lock:
                self._cache.pop(key, None)
                self._access_order.pop(key, None)
            return False
        
        return True
    
    async def clear(self) -> None:
        """Clear all cache"""
        async with self._lock:
            self._cache.clear()
            self._access_order.clear()
    
    async def _evict_lru(self) -> None:
        """Evict least recently used item"""
        if not self._access_order:
            return
        
        # Find oldest accessed key
        oldest_key = min(self._access_order.keys(), key=lambda k: self._access_order[k])
        self._cache.pop(oldest_key, None)
        self._access_order.pop(oldest_key, None)


class RedisCache(ICache):
    """Redis cache implementation"""
    
    def __init__(self, redis_url: str, key_prefix: str = "cache:"):
        self.redis_url = redis_url
        self.key_prefix = key_prefix
        self._redis = None
    
    async def _get_redis(self):
        """Get Redis connection"""
        if self._redis is None:
            try:
                import redis.asyncio as aioredis
                self._redis = await aioredis.from_url(self.redis_url)
            except ImportError:
                raise CacheError("get_connection", "redis", "redis package not installed")
            except Exception as e:
                raise CacheError("get_connection", "redis", str(e))
        return self._redis
    
    def _make_key(self, key: str) -> str:
        """Create prefixed key"""
        return f"{self.key_prefix}{key}"
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        try:
            redis = await self._get_redis()
            prefixed_key = self._make_key(key)
            
            # Get value from Redis
            value = await redis.get(prefixed_key)
            if value is None:
                return None
            
            # Deserialize value
            import pickle
            return pickle.loads(value)
            
        except Exception as e:
            raise CacheError("get", key, str(e))
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache"""
        try:
            redis = await self._get_redis()
            prefixed_key = self._make_key(key)
            
            # Serialize value
            import pickle
            serialized_value = pickle.dumps(value)
            
            # Set in Redis
            if ttl is not None:
                await redis.setex(prefixed_key, ttl, serialized_value)
            else:
                await redis.set(prefixed_key, serialized_value)
                
        except Exception as e:
            raise CacheError("set", key, str(e))
    
    async def delete(self, key: str) -> None:
        """Delete value from cache"""
        try:
            redis = await self._get_redis()
            prefixed_key = self._make_key(key)
            await redis.delete(prefixed_key)
            
        except Exception as e:
            raise CacheError("delete", key, str(e))
    
    async def exists(self, key: str) -> bool:
        """Check if key exists"""
        try:
            redis = await self._get_redis()
            prefixed_key = self._make_key(key)
            result = await redis.exists(prefixed_key)
            return bool(result)
            
        except Exception as e:
            raise CacheError("exists", key, str(e))
    
    async def clear(self) -> None:
        """Clear all cache with prefix"""
        try:
            redis = await self._get_redis()
            pattern = f"{self.key_prefix}*"
            
            # Find all keys with prefix
            keys = await redis.keys(pattern)
            if keys:
                await redis.delete(*keys)
                
        except Exception as e:
            raise CacheError("clear", "all", str(e))
    
    async def dispose(self) -> None:
        """Dispose Redis connection"""
        if self._redis:
            await self._redis.close()
            self._redis = None 