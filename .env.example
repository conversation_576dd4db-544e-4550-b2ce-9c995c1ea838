﻿# Cloudflare Secrets Store Configuration
# Copy this file to .env and fill in your actual values

# Cloudflare Account Settings
CLOUDFLARE_ACCOUNT_ID=your-account-id-here
CLOUDFLARE_API_TOKEN=your-api-token-here
CLOUDFLARE_STORE_ID=your-store-id-here

# Secret Management Configuration
SECRET_PROVIDER=cloudflare
SECRET_CACHE_TTL=300
FALLBACK_TO_ENV=true

# Application Secrets (these will be migrated to Cloudflare Secrets Store)
# DATABASE_PASSWORD=your-db-password
# JWT_SECRET_KEY=your-jwt-secret
# API_KEY=your-api-key

# Other Application Settings
APP_NAME=FastAPI Core Framework
APP_VERSION=1.0.0
DEBUG=false
ENVIRONMENT=production

# Database Configuration (non-secret parts)
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=laneswap_db
DATABASE_USER=laneswap_user
# DATABASE_PASSWORD will be stored in Cloudflare Secrets Store

# Redis Configuration (non-secret parts)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
# REDIS_PASSWORD will be stored in Cloudflare Secrets Store

# Monitoring and Logging
LOG_LEVEL=INFO
ENABLE_METRICS=true
METRICS_PORT=9090

# CORS Settings
CORS_ORIGINS=["http://localhost:3000", "https://yourdomain.com"]
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Security Settings
ENABLE_HTTPS_REDIRECT=true
ENABLE_SECURITY_HEADERS=true

# Instructions:
# 1. Copy this file to .env
# 2. Fill in your Cloudflare account details
# 3. Run: python setup_secret_management.py
# 4. Test the Cloudflare Secrets Store connection
# 5. Migrate your secrets from environment variables to Cloudflare
