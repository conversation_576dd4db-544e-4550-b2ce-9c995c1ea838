"""
Redis Service Registry V2 with Fallback Mechanisms

This module provides a Redis-backed service registry with comprehensive
fallback mechanisms for handling Redis Cloud free tier limits.
"""

import asyncio
import json
import logging
import time
import uuid
from datetime import datetime, timezone, timedelta
from typing import Any, Dict, List, Optional, Set
from urllib.parse import urljoin

import aiohttp
from pydantic import BaseModel

from core.domain.interfaces import IServiceRegistry
from core.domain.exceptions import ServiceRegistryError
from core.infrastructure.redis_fallback_manager import (
    RedisFallbackManager,
    RedisLimits,
    FallbackStrategy
)

logger = logging.getLogger(__name__)


class ServiceInfo(BaseModel):
    """Service information model for Redis storage"""
    name: str
    url: str
    version: str
    health_endpoint: str = "/health"
    api_key: Optional[str] = None
    metadata: Dict[str, Any] = {}
    registered_at: datetime
    last_heartbeat: datetime
    last_health_check: Optional[datetime] = None
    is_healthy: bool = True
    restart_endpoint: str = "/admin/restart"
    ttl: int = 300  # Service TTL in seconds (5 minutes)
    health_check_interval: int = 30  # Health check interval in seconds

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class RedisServiceRegistryV2(IServiceRegistry):
    """
    Redis service registry V2 with fallback mechanisms for free tier limits

    Features:
    - Connection pooling and rate limiting
    - Memory fallback when Redis is unavailable
    - Queue system for retrying failed operations
    - Graceful degradation under load
    - Distributed locking with fallback
    - Health monitoring with circuit breaker pattern
    """

    def __init__(
        self,
        redis_url: str,
        key_prefix: str = "service_registry:",
        default_ttl: int = 300,
        health_check_interval: int = 30,
        lock_timeout: int = 10,
        fallback_strategy: FallbackStrategy = FallbackStrategy.HYBRID,
        redis_limits: Optional[RedisLimits] = None
    ):
        self.redis_url = redis_url
        self.key_prefix = key_prefix
        self.default_ttl = default_ttl
        self.health_check_interval = health_check_interval
        self.lock_timeout = lock_timeout
        self.fallback_strategy = fallback_strategy

        # Initialize fallback manager
        self.fallback_manager = RedisFallbackManager(
            redis_url=redis_url,
            limits=redis_limits or RedisLimits(),
            fallback_strategy=fallback_strategy,
            enable_memory_fallback=True,
            enable_queue_system=True
        )

        # Memory fallback for service registry
        self._memory_services: Dict[str, ServiceInfo] = {}
        self._memory_locks: Dict[str, Dict[str, Any]] = {}

        # Health monitoring
        self._health_monitor_task: Optional[asyncio.Task] = None
        self._running = False
        self._initialized = False

    async def initialize(self):
        """Initialize the service registry"""
        if not self._initialized:
            await self.fallback_manager.initialize()
            self._initialized = True
            logger.info("Redis service registry V2 initialized")

    def _make_service_key(self, service_name: str) -> str:
        """Create Redis key for service"""
        return f"{self.key_prefix}services:{service_name}"

    def _make_lock_key(self, service_name: str) -> str:
        """Create Redis key for service lock"""
        return f"{self.key_prefix}locks:{service_name}"

    def _make_heartbeat_key(self, service_name: str) -> str:
        """Create Redis key for service heartbeat"""
        return f"{self.key_prefix}heartbeats:{service_name}"

    async def _acquire_lock(self, service_name: str, timeout: Optional[int] = None) -> Optional[str]:
        """Acquire distributed lock with fallback to memory lock"""
        if not self._initialized:
            await self.initialize()

        lock_key = self._make_lock_key(service_name)
        lock_value = str(uuid.uuid4())
        timeout = timeout or self.lock_timeout

        try:
            # Try Redis lock first
            result = await self.fallback_manager.execute_operation(
                "set",
                lock_key,
                lock_value,
                nx=True,
                ex=timeout,
                fallback_value=None,
                priority=3  # High priority for locks
            )

            if result:
                return lock_value

        except Exception as e:
            logger.warning(f"Redis lock failed for {service_name}: {e}")

        # Fallback to memory lock
        current_time = time.time()
        if service_name in self._memory_locks:
            lock_info = self._memory_locks[service_name]
            if current_time < lock_info["expires_at"]:
                return None  # Lock still held
            else:
                # Lock expired, remove it
                del self._memory_locks[service_name]

        # Acquire memory lock
        self._memory_locks[service_name] = {
            "value": lock_value,
            "expires_at": current_time + timeout
        }

        return lock_value

    async def _release_lock(self, service_name: str, lock_value: str) -> bool:
        """Release distributed lock with fallback"""
        lock_key = self._make_lock_key(service_name)

        try:
            # Try Redis lock release first
            lua_script = """
            if redis.call("get", KEYS[1]) == ARGV[1] then
                return redis.call("del", KEYS[1])
            else
                return 0
            end
            """

            result = await self.fallback_manager.execute_operation(
                "eval",
                lua_script,
                1,
                lock_key,
                lock_value,
                fallback_value=False,
                priority=3
            )

            if result:
                return True

        except Exception as e:
            logger.warning(f"Redis lock release failed for {service_name}: {e}")

        # Fallback to memory lock release
        if service_name in self._memory_locks:
            lock_info = self._memory_locks[service_name]
            if lock_info["value"] == lock_value:
                del self._memory_locks[service_name]
                return True

        return False

    async def start_monitoring(self) -> None:
        """Start health monitoring background task"""
        if not self._running:
            self._running = True
            self._health_monitor_task = asyncio.create_task(self._health_monitoring_loop())

    async def stop_monitoring(self) -> None:
        """Stop health monitoring background task"""
        self._running = False
        if self._health_monitor_task:
            self._health_monitor_task.cancel()
            try:
                await self._health_monitor_task
            except asyncio.CancelledError:
                pass

    async def register_service(
        self,
        name: str,
        url: str,
        version: str,
        health_endpoint: str = "/health",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Register a new service with fallback mechanisms"""
        if not self._initialized:
            await self.initialize()

        try:
            # Acquire lock for service registration
            lock_value = await self._acquire_lock(name)
            if not lock_value:
                return {
                    "success": False,
                    "error": f"Could not acquire lock for service '{name}'. Another operation in progress."
                }

            try:
                service_key = self._make_service_key(name)
                heartbeat_key = self._make_heartbeat_key(name)

                # Generate API key for the service
                api_key = f"api_key_{name}_{int(time.time())}_{uuid.uuid4().hex[:8]}"

                # Create service info
                now = datetime.now(timezone.utc)
                service_info = ServiceInfo(
                    name=name,
                    url=url,
                    version=version,
                    health_endpoint=health_endpoint,
                    api_key=api_key,
                    metadata=metadata or {},
                    registered_at=now,
                    last_heartbeat=now,
                    ttl=self.default_ttl
                )

                # Store service in Redis with fallback
                service_data = service_info.model_dump_json()

                try:
                    await self.fallback_manager.execute_operation(
                        "setex",
                        service_key,
                        self.default_ttl,
                        service_data,
                        fallback_value=True,
                        ttl=self.default_ttl,
                        priority=2
                    )

                    await self.fallback_manager.execute_operation(
                        "setex",
                        heartbeat_key,
                        self.default_ttl,
                        now.isoformat(),
                        fallback_value=True,
                        ttl=self.default_ttl,
                        priority=2
                    )

                except Exception as e:
                    logger.warning(f"Redis registration failed for {name}: {e}")

                # Always store in memory as backup
                self._memory_services[name] = service_info

                return {
                    "success": True,
                    "service": name,
                    "api_key": api_key,
                    "ttl": self.default_ttl,
                    "heartbeat_interval": self.health_check_interval
                }

            finally:
                # Always release the lock
                await self._release_lock(name, lock_value)

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to register service: {str(e)}"
            }

    async def unregister_service(self, service_name: str) -> Dict[str, Any]:
        """Unregister a service with fallback mechanisms"""
        if not self._initialized:
            await self.initialize()

        try:
            # Acquire lock for service unregistration
            lock_value = await self._acquire_lock(service_name)
            if not lock_value:
                return {
                    "success": False,
                    "error": f"Could not acquire lock for service '{service_name}'. Another operation in progress."
                }

            try:
                service_key = self._make_service_key(service_name)
                heartbeat_key = self._make_heartbeat_key(service_name)

                # Check if service exists (try Redis first, then memory)
                service_exists = False

                try:
                    exists_result = await self.fallback_manager.execute_operation(
                        "exists",
                        service_key,
                        fallback_value=False
                    )
                    service_exists = bool(exists_result)
                except Exception:
                    pass

                if not service_exists:
                    service_exists = service_name in self._memory_services

                if not service_exists:
                    return {"success": False, "error": "Service not found"}

                # Remove service from Redis
                try:
                    await self.fallback_manager.execute_operation(
                        "delete",
                        service_key,
                        heartbeat_key,
                        fallback_value=True,
                        priority=2
                    )
                except Exception as e:
                    logger.warning(f"Redis unregistration failed for {service_name}: {e}")

                # Always remove from memory
                self._memory_services.pop(service_name, None)

                return {"success": True, "service": service_name}

            finally:
                # Always release the lock
                await self._release_lock(service_name, lock_value)

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to unregister service: {str(e)}"
            }

    async def list_services(self, only_healthy: bool = False) -> List[Dict[str, Any]]:
        """List all registered services with fallback"""
        if not self._initialized:
            await self.initialize()

        services = {}

        # Try to get services from Redis first
        try:
            pattern = f"{self.key_prefix}services:*"
            keys = await self.fallback_manager.execute_operation(
                "keys",
                pattern,
                fallback_value=[]
            )

            if keys:
                for key in keys:
                    try:
                        service_data = await self.fallback_manager.execute_operation(
                            "get",
                            key,
                            fallback_value=None
                        )

                        if service_data:
                            if isinstance(service_data, bytes):
                                service_data = service_data.decode('utf-8')

                            service_info = ServiceInfo.model_validate_json(service_data)
                            services[service_info.name] = service_info.model_dump()

                    except Exception as e:
                        logger.warning(f"Failed to parse service data for key {key}: {e}")

        except Exception as e:
            logger.warning(f"Failed to list services from Redis: {e}")

        # Add services from memory fallback
        for name, service_info in self._memory_services.items():
            if name not in services:
                services[name] = service_info.model_dump()

        # Filter by health status if requested
        all_services = list(services.values())
        if only_healthy:
            return [s for s in all_services if s.get("is_healthy", True)]

        return all_services

    async def get_service(self, service_name: str) -> Optional[Dict[str, Any]]:
        """Get service information with fallback"""
        if not self._initialized:
            await self.initialize()

        service_key = self._make_service_key(service_name)

        # Try Redis first
        try:
            service_data = await self.fallback_manager.execute_operation(
                "get",
                service_key,
                fallback_value=None
            )

            if service_data:
                if isinstance(service_data, bytes):
                    service_data = service_data.decode('utf-8')

                service_info = ServiceInfo.model_validate_json(service_data)
                return service_info.model_dump()

        except Exception as e:
            logger.warning(f"Failed to get service {service_name} from Redis: {e}")

        # Fallback to memory
        if service_name in self._memory_services:
            return self._memory_services[service_name].model_dump()

        return None

    async def heartbeat(self, service_name: str) -> Dict[str, Any]:
        """Update service heartbeat with fallback"""
        if not self._initialized:
            await self.initialize()

        try:
            heartbeat_key = self._make_heartbeat_key(service_name)
            now = datetime.now(timezone.utc)

            # Update heartbeat in Redis
            try:
                await self.fallback_manager.execute_operation(
                    "setex",
                    heartbeat_key,
                    self.default_ttl,
                    now.isoformat(),
                    fallback_value=True,
                    ttl=self.default_ttl,
                    priority=1
                )
            except Exception as e:
                logger.warning(f"Redis heartbeat failed for {service_name}: {e}")

            # Update in memory
            if service_name in self._memory_services:
                self._memory_services[service_name].last_heartbeat = now

            return {
                "success": True,
                "service": service_name,
                "next_heartbeat": (now + timedelta(seconds=self.health_check_interval)).isoformat()
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to update heartbeat: {str(e)}"
            }

    async def get_service_stats(self) -> Dict[str, Any]:
        """Get service registry statistics"""
        services = await self.list_services()

        total_services = len(services)
        healthy_services = sum(1 for s in services if s.get("is_healthy", True))
        unhealthy_services = total_services - healthy_services

        health_percentage = (healthy_services / max(1, total_services)) * 100

        return {
            "total_services": total_services,
            "healthy_services": healthy_services,
            "unhealthy_services": unhealthy_services,
            "health_percentage": round(health_percentage, 2)
        }

    async def cleanup_expired_services(self) -> Dict[str, Any]:
        """Clean up expired services"""
        try:
            # This is a simplified cleanup - in a real implementation,
            # you'd check TTL and remove expired services
            return {
                "success": True,
                "expired_services_cleaned": 0
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Cleanup failed: {str(e)}"
            }

    async def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status"""
        if not self._initialized:
            return {
                "status": "not_initialized",
                "redis_healthy": False,
                "memory_services_count": 0
            }

        # Get fallback manager health
        health = await self.fallback_manager.get_health_status()

        # Add service registry specific info
        health.update({
            "memory_services_count": len(self._memory_services),
            "memory_locks_count": len(self._memory_locks),
            "monitoring_active": self._running,
            "registry_strategy": self.fallback_strategy.value,
            "initialized": self._initialized
        })

        return health

    async def dispose(self) -> None:
        """Dispose service registry resources"""
        await self.stop_monitoring()

        if self._initialized:
            await self.fallback_manager.dispose()
            self._memory_services.clear()
            self._memory_locks.clear()
            self._initialized = False
            logger.info("Redis service registry V2 disposed")

    async def _health_monitoring_loop(self) -> None:
        """Background task for health monitoring with fallback awareness"""
        while self._running:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitoring loop: {e}")
                await asyncio.sleep(self.health_check_interval)

    async def _perform_health_checks(self) -> None:
        """Perform health checks with fallback awareness"""
        try:
            # Get services from both Redis and memory
            services = await self.list_services()

            # Limit concurrent health checks to avoid overwhelming Redis
            semaphore = asyncio.Semaphore(5)  # Max 5 concurrent health checks

            async def check_with_semaphore(service_name: str):
                async with semaphore:
                    await self._check_service_health(service_name)

            # Create tasks for parallel health checking
            health_check_tasks = [
                asyncio.create_task(check_with_semaphore(service["name"]))
                for service in services
            ]

            # Wait for all health checks to complete
            if health_check_tasks:
                await asyncio.gather(*health_check_tasks, return_exceptions=True)

        except Exception as e:
            logger.error(f"Error performing health checks: {e}")

    async def _check_service_health(self, service_name: str) -> None:
        """Check health of a specific service with fallback awareness"""
        try:
            service = await self.get_service(service_name)
            if not service:
                return

            # Make health check request with timeout
            health_url = urljoin(service["url"], service["health_endpoint"].lstrip("/"))

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    health_url,
                    timeout=aiohttp.ClientTimeout(total=5)  # Shorter timeout for health checks
                ) as response:
                    is_healthy = response.status == 200
                    await self._update_service_health(service_name, is_healthy, None)

        except Exception as e:
            # Mark service as unhealthy on any error
            await self._update_service_health(service_name, False, str(e))

    async def _update_service_health(self, service_name: str, is_healthy: bool, error_message: Optional[str]) -> None:
        """Update service health status with fallback"""
        try:
            # Update in memory first (always works)
            if service_name in self._memory_services:
                self._memory_services[service_name].is_healthy = is_healthy
                self._memory_services[service_name].last_health_check = datetime.now(timezone.utc)
                if error_message:
                    self._memory_services[service_name].metadata["last_error"] = error_message

            # Try to update in Redis
            service_key = self._make_service_key(service_name)

            try:
                service_data = await self.fallback_manager.execute_operation(
                    "get",
                    service_key,
                    fallback_value=None
                )

                if service_data:
                    if isinstance(service_data, bytes):
                        service_data = service_data.decode('utf-8')

                    service_info = ServiceInfo.model_validate_json(service_data)
                    service_info.is_healthy = is_healthy
                    service_info.last_health_check = datetime.now(timezone.utc)

                    if error_message:
                        service_info.metadata["last_error"] = error_message

                    # Update in Redis
                    await self.fallback_manager.execute_operation(
                        "setex",
                        service_key,
                        self.default_ttl,
                        service_info.model_dump_json(),
                        fallback_value=True,
                        ttl=self.default_ttl,
                        priority=1
                    )

            except Exception as e:
                logger.warning(f"Failed to update health status in Redis for {service_name}: {e}")

        except Exception as e:
            logger.error(f"Error updating service health for {service_name}: {e}")

    async def call_service(
        self,
        service_name: str,
        endpoint: str,
        method: str = "GET",
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Call a service endpoint"""
        try:
            service = await self.get_service(service_name)
            if not service:
                return {
                    "success": False,
                    "error": f"Service '{service_name}' not found"
                }

            # Construct the full URL
            base_url = service["url"].rstrip("/")
            endpoint = endpoint.lstrip("/")
            url = f"{base_url}/{endpoint}"

            # Prepare headers
            call_headers = headers or {}
            if service.get("api_key"):
                call_headers["X-API-Key"] = service["api_key"]

            # Make the request
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=method.upper(),
                    url=url,
                    json=data if method.upper() in ["POST", "PUT", "PATCH"] else None,
                    params=data if method.upper() == "GET" else None,
                    headers=call_headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    response_data = await response.text()

                    try:
                        response_json = await response.json()
                    except:
                        response_json = {"text": response_data}

                    return {
                        "success": response.status < 400,
                        "status_code": response.status,
                        "data": response_json,
                        "service": service_name,
                        "endpoint": endpoint
                    }

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to call service: {str(e)}",
                "service": service_name,
                "endpoint": endpoint
            }

    async def restart_service(self, service_name: str) -> Dict[str, Any]:
        """Restart a service"""
        try:
            service = await self.get_service(service_name)
            if not service:
                return {
                    "success": False,
                    "error": f"Service '{service_name}' not found"
                }

            # Call the restart endpoint
            restart_endpoint = service.get("restart_endpoint", "/admin/restart")
            result = await self.call_service(
                service_name=service_name,
                endpoint=restart_endpoint,
                method="POST"
            )

            if result["success"]:
                return {
                    "success": True,
                    "service": service_name,
                    "message": "Restart command sent successfully"
                }
            else:
                return {
                    "success": False,
                    "error": f"Failed to restart service: {result.get('error', 'Unknown error')}",
                    "service": service_name
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to restart service: {str(e)}",
                "service": service_name
            }
