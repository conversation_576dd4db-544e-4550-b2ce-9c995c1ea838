"""
Scoring System Implementation

This module provides concrete implementations of the scoring interfaces
for evaluating service performance, reliability, and overall quality.
"""

import asyncio
import math
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone, timedelta
from collections import defaultdict

from core.domain.interfaces import (
    IScoringEngine, 
    IMetricsAggregator, 
    ScoreType,
    IServiceRegistry,
    IMetricsCollector
)


class MetricsAggregator(IMetricsAggregator):
    """Aggregates metrics for scoring calculations"""
    
    def __init__(self, metrics_collector: IMetricsCollector, service_registry: IServiceRegistry):
        self.metrics_collector = metrics_collector
        self.service_registry = service_registry
        self._cache = {}
        self._cache_ttl = 60  # Cache for 1 minute
        
    async def aggregate_metrics(
        self, 
        service_name: str, 
        time_window: int = 300
    ) -> Dict[str, Any]:
        """Aggregate metrics for a service over time window"""
        cache_key = f"{service_name}_{time_window}"
        now = datetime.now(timezone.utc)
        
        # Check cache
        if cache_key in self._cache:
            cached_data, timestamp = self._cache[cache_key]
            if (now - timestamp).total_seconds() < self._cache_ttl:
                return cached_data
        
        # Get current metrics
        metrics = await self.metrics_collector.get_metrics()
        
        # Aggregate metrics for the service
        aggregated = {
            "service_name": service_name,
            "time_window": time_window,
            "timestamp": now.isoformat(),
            "request_count": 0,
            "error_count": 0,
            "total_response_time": 0.0,
            "average_response_time": 0.0,
            "error_rate": 0.0,
            "throughput": 0.0,
            "availability": 1.0
        }
        
        # Process counters
        for metric_name, value in metrics.get("counters", {}).items():
            if service_name in str(metric_name):
                if "requests" in metric_name:
                    aggregated["request_count"] += value
                elif "errors" in metric_name:
                    aggregated["error_count"] += value
        
        # Process timers for response times
        for metric_name, values in metrics.get("timers", {}).items():
            if service_name in str(metric_name) and "response_time" in metric_name:
                if values:
                    aggregated["total_response_time"] = sum(values)
                    aggregated["average_response_time"] = sum(values) / len(values)
        
        # Calculate derived metrics
        if aggregated["request_count"] > 0:
            aggregated["error_rate"] = aggregated["error_count"] / aggregated["request_count"]
            aggregated["throughput"] = aggregated["request_count"] / (time_window / 60)  # requests per minute
        
        # Cache the result
        self._cache[cache_key] = (aggregated, now)
        
        return aggregated
    
    async def get_performance_metrics(self, service_name: str) -> Dict[str, float]:
        """Get performance metrics for scoring"""
        aggregated = await self.aggregate_metrics(service_name)
        
        return {
            "average_response_time": aggregated["average_response_time"],
            "throughput": aggregated["throughput"],
            "cpu_usage": 0.5,  # TODO: Get from actual monitoring
            "memory_usage": 0.6,  # TODO: Get from actual monitoring
            "disk_io": 0.3  # TODO: Get from actual monitoring
        }
    
    async def get_reliability_metrics(self, service_name: str) -> Dict[str, float]:
        """Get reliability metrics for scoring"""
        aggregated = await self.aggregate_metrics(service_name)
        
        return {
            "error_rate": aggregated["error_rate"],
            "uptime": 0.99,  # TODO: Calculate from actual uptime data
            "mtbf": 168.0,  # Mean time between failures in hours
            "mttr": 0.5,   # Mean time to recovery in hours
            "success_rate": 1.0 - aggregated["error_rate"]
        }
    
    async def get_availability_metrics(self, service_name: str) -> Dict[str, float]:
        """Get availability metrics for scoring"""
        return {
            "uptime_percentage": 99.9,  # TODO: Calculate from actual data
            "response_success_rate": 99.5,
            "health_check_success_rate": 100.0,
            "service_discovery_success_rate": 99.8
        }


class ScoringEngine(IScoringEngine):
    """Main scoring engine implementation"""
    
    def __init__(self, metrics_aggregator: IMetricsAggregator):
        self.metrics_aggregator = metrics_aggregator
        self.score_weights = {
            ScoreType.PERFORMANCE: {
                "response_time": 0.4,
                "throughput": 0.3,
                "resource_usage": 0.3
            },
            ScoreType.RELIABILITY: {
                "error_rate": 0.4,
                "uptime": 0.3,
                "recovery_time": 0.3
            },
            ScoreType.AVAILABILITY: {
                "uptime_percentage": 0.5,
                "response_success_rate": 0.3,
                "health_check_success": 0.2
            },
            ScoreType.COMPOSITE: {
                ScoreType.PERFORMANCE: 0.4,
                ScoreType.RELIABILITY: 0.3,
                ScoreType.AVAILABILITY: 0.3
            }
        }
        
    async def calculate_score(
        self,
        service_name: str,
        score_type: ScoreType,
        metrics: Optional[Dict[str, Any]] = None
    ) -> float:
        """Calculate score for a service"""
        if metrics is None:
            if score_type == ScoreType.PERFORMANCE:
                metrics = await self.metrics_aggregator.get_performance_metrics(service_name)
            elif score_type == ScoreType.RELIABILITY:
                metrics = await self.metrics_aggregator.get_reliability_metrics(service_name)
            elif score_type == ScoreType.AVAILABILITY:
                metrics = await self.metrics_aggregator.get_availability_metrics(service_name)
            else:
                # For composite score, calculate individual scores
                return await self._calculate_composite_score(service_name)
        
        if score_type == ScoreType.PERFORMANCE:
            return self._calculate_performance_score(metrics)
        elif score_type == ScoreType.RELIABILITY:
            return self._calculate_reliability_score(metrics)
        elif score_type == ScoreType.AVAILABILITY:
            return self._calculate_availability_score(metrics)
        elif score_type == ScoreType.COMPOSITE:
            return await self._calculate_composite_score(service_name)
        else:
            raise ValueError(f"Unknown score type: {score_type}")
    
    def _calculate_performance_score(self, metrics: Dict[str, float]) -> float:
        """Calculate performance score (0-100)"""
        weights = self.score_weights[ScoreType.PERFORMANCE]
        
        # Response time score (lower is better)
        response_time = metrics.get("average_response_time", 1.0)
        response_time_score = max(0, 100 - (response_time * 100))  # Assume 1s = 0 score
        
        # Throughput score (higher is better)
        throughput = metrics.get("throughput", 0)
        throughput_score = min(100, throughput * 10)  # Assume 10 req/min = 100 score
        
        # Resource usage score (lower is better)
        cpu_usage = metrics.get("cpu_usage", 0.5)
        memory_usage = metrics.get("memory_usage", 0.5)
        resource_score = max(0, 100 - ((cpu_usage + memory_usage) * 50))
        
        # Weighted average
        score = (
            response_time_score * weights["response_time"] +
            throughput_score * weights["throughput"] +
            resource_score * weights["resource_usage"]
        )
        
        return min(100, max(0, score))
    
    def _calculate_reliability_score(self, metrics: Dict[str, float]) -> float:
        """Calculate reliability score (0-100)"""
        weights = self.score_weights[ScoreType.RELIABILITY]
        
        # Error rate score (lower is better)
        error_rate = metrics.get("error_rate", 0.0)
        error_score = max(0, 100 - (error_rate * 1000))  # 10% error = 0 score
        
        # Uptime score
        uptime = metrics.get("uptime", 0.99)
        uptime_score = uptime * 100
        
        # Recovery time score (lower is better)
        mttr = metrics.get("mttr", 1.0)  # Mean time to recovery in hours
        recovery_score = max(0, 100 - (mttr * 50))  # 2 hours = 0 score
        
        # Weighted average
        score = (
            error_score * weights["error_rate"] +
            uptime_score * weights["uptime"] +
            recovery_score * weights["recovery_time"]
        )
        
        return min(100, max(0, score))
    
    def _calculate_availability_score(self, metrics: Dict[str, float]) -> float:
        """Calculate availability score (0-100)"""
        weights = self.score_weights[ScoreType.AVAILABILITY]
        
        # Direct percentage scores
        uptime_score = metrics.get("uptime_percentage", 99.0)
        response_score = metrics.get("response_success_rate", 99.0)
        health_score = metrics.get("health_check_success_rate", 100.0)
        
        # Weighted average
        score = (
            uptime_score * weights["uptime_percentage"] +
            response_score * weights["response_success_rate"] +
            health_score * weights["health_check_success"]
        )
        
        return min(100, max(0, score))
    
    async def _calculate_composite_score(self, service_name: str) -> float:
        """Calculate composite score from all score types"""
        weights = self.score_weights[ScoreType.COMPOSITE]
        
        # Calculate individual scores
        performance_score = await self.calculate_score(service_name, ScoreType.PERFORMANCE)
        reliability_score = await self.calculate_score(service_name, ScoreType.RELIABILITY)
        availability_score = await self.calculate_score(service_name, ScoreType.AVAILABILITY)
        
        # Weighted average
        composite_score = (
            performance_score * weights[ScoreType.PERFORMANCE] +
            reliability_score * weights[ScoreType.RELIABILITY] +
            availability_score * weights[ScoreType.AVAILABILITY]
        )
        
        return min(100, max(0, composite_score))
    
    async def get_composite_score(
        self,
        service_name: str,
        weights: Optional[Dict[ScoreType, float]] = None
    ) -> Dict[str, Any]:
        """Get composite score with breakdown"""
        if weights:
            # Temporarily update weights
            original_weights = self.score_weights[ScoreType.COMPOSITE].copy()
            self.score_weights[ScoreType.COMPOSITE].update(weights)
        
        try:
            # Calculate individual scores
            performance_score = await self.calculate_score(service_name, ScoreType.PERFORMANCE)
            reliability_score = await self.calculate_score(service_name, ScoreType.RELIABILITY)
            availability_score = await self.calculate_score(service_name, ScoreType.AVAILABILITY)
            composite_score = await self.calculate_score(service_name, ScoreType.COMPOSITE)
            
            return {
                "service_name": service_name,
                "composite_score": composite_score,
                "breakdown": {
                    "performance": performance_score,
                    "reliability": reliability_score,
                    "availability": availability_score
                },
                "weights": self.score_weights[ScoreType.COMPOSITE].copy(),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "grade": self._get_score_grade(composite_score)
            }
        finally:
            if weights:
                # Restore original weights
                self.score_weights[ScoreType.COMPOSITE] = original_weights
    
    def _get_score_grade(self, score: float) -> str:
        """Convert numeric score to letter grade"""
        if score >= 90:
            return "A"
        elif score >= 80:
            return "B"
        elif score >= 70:
            return "C"
        elif score >= 60:
            return "D"
        else:
            return "F"
    
    async def get_service_ranking(
        self,
        score_type: ScoreType = ScoreType.COMPOSITE
    ) -> List[Dict[str, Any]]:
        """Get ranked list of services by score"""
        # TODO: Get list of services from service registry
        # For now, return empty list as this requires service registry integration
        return []
    
    async def update_score_weights(self, weights: Dict[ScoreType, float]) -> None:
        """Update scoring weights"""
        if ScoreType.COMPOSITE in weights:
            # Normalize weights to sum to 1.0
            total = sum(weights[ScoreType.COMPOSITE].values())
            if total > 0:
                for key in weights[ScoreType.COMPOSITE]:
                    weights[ScoreType.COMPOSITE][key] /= total
        
        # Update weights
        for score_type, weight_dict in weights.items():
            if score_type in self.score_weights:
                self.score_weights[score_type].update(weight_dict)
