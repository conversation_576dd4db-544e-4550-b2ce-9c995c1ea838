"""
Supabase Client Infrastructure

This module provides a comprehensive Supabase client with all services:
- Database (PostgreSQL with RLS)
- Authentication & Authorization
- Real-time subscriptions
- Storage & file management
- Edge Functions
- Auto-generated APIs
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import datetime, timezone

from supabase import create_client, Client
from supabase.lib.client_options import ClientOptions
from gotrue import SyncGoTrueClient
from postgrest import SyncPostgrestClient
from realtime import Socket
from storage3 import SyncStorageClient
from functions import SyncFunctionsClient

from core.config.settings import SupabaseSettings

logger = logging.getLogger(__name__)


class SupabaseClientManager:
    """
    Comprehensive Supabase client manager with all services
    
    Features:
    - Database operations with RLS
    - Authentication & user management
    - Real-time subscriptions
    - File storage & CDN
    - Edge Functions
    - Auto-generated REST/GraphQL APIs
    """

    def __init__(self, settings: SupabaseSettings):
        self.settings = settings
        self._client: Optional[Client] = None
        self._service_client: Optional[Client] = None
        self._initialized = False
        
        # Service clients
        self._auth_client: Optional[SyncGoTrueClient] = None
        self._db_client: Optional[SyncPostgrestClient] = None
        self._realtime_client: Optional[Socket] = None
        self._storage_client: Optional[SyncStorageClient] = None
        self._functions_client: Optional[SyncFunctionsClient] = None
        
        # Real-time subscriptions
        self._subscriptions: Dict[str, Any] = {}
        self._realtime_callbacks: Dict[str, List[Callable]] = {}

    async def initialize(self) -> None:
        """Initialize Supabase client and all services"""
        if self._initialized:
            return

        if not self.settings.supabase_url or not self.settings.supabase_anon_key:
            raise ValueError("Supabase URL and anonymous key are required")

        try:
            # Create client options
            options = ClientOptions(
                auto_refresh_token=True,
                persist_session=True,
                detect_session_in_url=True,
                headers={
                    "User-Agent": "laneswap-core/1.0.0"
                }
            )

            # Create public client (for client-side operations)
            self._client = create_client(
                self.settings.supabase_url,
                self.settings.supabase_anon_key,
                options=options
            )

            # Create service client (for server-side operations)
            if self.settings.supabase_service_key:
                self._service_client = create_client(
                    self.settings.supabase_url,
                    self.settings.supabase_service_key,
                    options=options
                )

            # Initialize service clients
            await self._initialize_services()
            
            self._initialized = True
            logger.info("Supabase client initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {e}")
            raise

    async def _initialize_services(self) -> None:
        """Initialize individual Supabase services"""
        
        # Database client (PostgREST)
        self._db_client = self._client.postgrest
        
        # Authentication client
        if self.settings.supabase_auth_enabled:
            self._auth_client = self._client.auth
            logger.info("Supabase Auth initialized")
        
        # Real-time client
        if self.settings.supabase_realtime_enabled:
            self._realtime_client = self._client.realtime
            logger.info("Supabase Realtime initialized")
        
        # Storage client
        if self.settings.supabase_storage_enabled:
            self._storage_client = self._client.storage
            await self._setup_storage_buckets()
            logger.info("Supabase Storage initialized")
        
        # Functions client
        if self.settings.supabase_functions_enabled:
            self._functions_client = self._client.functions
            logger.info("Supabase Functions initialized")

    async def _setup_storage_buckets(self) -> None:
        """Setup default storage buckets"""
        try:
            # Create default bucket if it doesn't exist
            bucket_name = self.settings.supabase_storage_bucket
            
            # Check if bucket exists
            buckets = self._storage_client.list_buckets()
            bucket_exists = any(bucket.name == bucket_name for bucket in buckets)
            
            if not bucket_exists:
                # Create bucket with public access
                self._storage_client.create_bucket(
                    bucket_name,
                    options={
                        "public": True,
                        "file_size_limit": self.settings.supabase_storage_max_file_size,
                        "allowed_mime_types": self.settings.supabase_storage_allowed_types
                    }
                )
                logger.info(f"Created storage bucket: {bucket_name}")
            
        except Exception as e:
            logger.warning(f"Failed to setup storage buckets: {e}")

    # Database Operations
    def table(self, table_name: str, use_service_key: bool = False):
        """Get table client for database operations"""
        client = self._service_client if use_service_key and self._service_client else self._client
        return client.table(table_name)

    def rpc(self, function_name: str, params: Optional[Dict] = None, use_service_key: bool = False):
        """Call database function (RPC)"""
        client = self._service_client if use_service_key and self._service_client else self._client
        return client.rpc(function_name, params or {})

    # Authentication Operations
    def sign_up(self, email: str, password: str, **kwargs):
        """Sign up new user"""
        if not self._auth_client:
            raise RuntimeError("Auth client not initialized")
        return self._auth_client.sign_up({"email": email, "password": password, **kwargs})

    def sign_in(self, email: str, password: str):
        """Sign in user"""
        if not self._auth_client:
            raise RuntimeError("Auth client not initialized")
        return self._auth_client.sign_in_with_password({"email": email, "password": password})

    def sign_out(self):
        """Sign out current user"""
        if not self._auth_client:
            raise RuntimeError("Auth client not initialized")
        return self._auth_client.sign_out()

    def get_user(self, jwt_token: Optional[str] = None):
        """Get current user"""
        if not self._auth_client:
            raise RuntimeError("Auth client not initialized")
        
        if jwt_token:
            return self._auth_client.get_user(jwt_token)
        return self._auth_client.get_user()

    def refresh_session(self, refresh_token: str):
        """Refresh user session"""
        if not self._auth_client:
            raise RuntimeError("Auth client not initialized")
        return self._auth_client.refresh_session(refresh_token)

    # Real-time Operations
    def subscribe_to_table(self, table_name: str, callback: Callable, event: str = "*"):
        """Subscribe to real-time table changes"""
        if not self._realtime_client:
            raise RuntimeError("Realtime client not initialized")
        
        subscription_key = f"{table_name}:{event}"
        
        # Store callback
        if subscription_key not in self._realtime_callbacks:
            self._realtime_callbacks[subscription_key] = []
        self._realtime_callbacks[subscription_key].append(callback)
        
        # Create subscription
        channel = self._realtime_client.channel(f"table:{table_name}")
        channel.on(event, callback)
        
        # Subscribe and store reference
        subscription = channel.subscribe()
        self._subscriptions[subscription_key] = subscription
        
        logger.info(f"Subscribed to {table_name} table changes")
        return subscription

    def unsubscribe_from_table(self, table_name: str, event: str = "*"):
        """Unsubscribe from real-time table changes"""
        subscription_key = f"{table_name}:{event}"
        
        if subscription_key in self._subscriptions:
            subscription = self._subscriptions[subscription_key]
            subscription.unsubscribe()
            del self._subscriptions[subscription_key]
            
            if subscription_key in self._realtime_callbacks:
                del self._realtime_callbacks[subscription_key]
            
            logger.info(f"Unsubscribed from {table_name} table changes")

    # Storage Operations
    def upload_file(self, bucket: str, path: str, file_data: bytes, **options):
        """Upload file to storage"""
        if not self._storage_client:
            raise RuntimeError("Storage client not initialized")
        
        return self._storage_client.from_(bucket).upload(path, file_data, **options)

    def download_file(self, bucket: str, path: str):
        """Download file from storage"""
        if not self._storage_client:
            raise RuntimeError("Storage client not initialized")
        
        return self._storage_client.from_(bucket).download(path)

    def delete_file(self, bucket: str, path: str):
        """Delete file from storage"""
        if not self._storage_client:
            raise RuntimeError("Storage client not initialized")
        
        return self._storage_client.from_(bucket).remove([path])

    def get_public_url(self, bucket: str, path: str):
        """Get public URL for file"""
        if not self._storage_client:
            raise RuntimeError("Storage client not initialized")
        
        return self._storage_client.from_(bucket).get_public_url(path)

    def create_signed_url(self, bucket: str, path: str, expires_in: int = 3600):
        """Create signed URL for private file"""
        if not self._storage_client:
            raise RuntimeError("Storage client not initialized")
        
        return self._storage_client.from_(bucket).create_signed_url(path, expires_in)

    # Edge Functions Operations
    def invoke_function(self, function_name: str, payload: Optional[Dict] = None, **options):
        """Invoke edge function"""
        if not self._functions_client:
            raise RuntimeError("Functions client not initialized")
        
        return self._functions_client.invoke(function_name, payload, **options)

    # Health and Status
    async def health_check(self) -> Dict[str, Any]:
        """Check health of all Supabase services"""
        health_status = {
            "initialized": self._initialized,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "services": {}
        }

        if not self._initialized:
            health_status["status"] = "not_initialized"
            return health_status

        try:
            # Test database connection
            result = self.table("_health_check").select("*").limit(1).execute()
            health_status["services"]["database"] = {
                "status": "healthy",
                "response_time_ms": getattr(result, 'response_time', 0)
            }
        except Exception as e:
            health_status["services"]["database"] = {
                "status": "unhealthy",
                "error": str(e)
            }

        # Test auth service
        if self._auth_client:
            try:
                # Simple auth health check
                health_status["services"]["auth"] = {"status": "healthy"}
            except Exception as e:
                health_status["services"]["auth"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }

        # Test storage service
        if self._storage_client:
            try:
                buckets = self._storage_client.list_buckets()
                health_status["services"]["storage"] = {
                    "status": "healthy",
                    "buckets_count": len(buckets)
                }
            except Exception as e:
                health_status["services"]["storage"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }

        # Overall status
        all_healthy = all(
            service.get("status") == "healthy" 
            for service in health_status["services"].values()
        )
        health_status["status"] = "healthy" if all_healthy else "degraded"

        return health_status

    async def dispose(self) -> None:
        """Clean up resources"""
        # Unsubscribe from all real-time subscriptions
        for subscription_key in list(self._subscriptions.keys()):
            table_name, event = subscription_key.split(":", 1)
            self.unsubscribe_from_table(table_name, event)

        # Close real-time connection
        if self._realtime_client:
            try:
                self._realtime_client.disconnect()
            except Exception as e:
                logger.warning(f"Error disconnecting realtime client: {e}")

        self._initialized = False
        logger.info("Supabase client disposed")

    # Convenience properties
    @property
    def client(self) -> Client:
        """Get public Supabase client"""
        if not self._client:
            raise RuntimeError("Supabase client not initialized")
        return self._client

    @property
    def service_client(self) -> Optional[Client]:
        """Get service role Supabase client"""
        return self._service_client

    @property
    def auth(self):
        """Get auth client"""
        return self._auth_client

    @property
    def storage(self):
        """Get storage client"""
        return self._storage_client

    @property
    def functions(self):
        """Get functions client"""
        return self._functions_client
