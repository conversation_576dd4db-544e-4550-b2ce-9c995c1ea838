"""
Redis Cache with Fallback Mechanisms

This module provides a Redis cache implementation with comprehensive
fallback mechanisms for handling Redis Cloud free tier limits.
"""

import asyncio
import logging
import pickle
from typing import Any, Optional, Dict, List
from datetime import datetime, timezone

from core.domain.interfaces import ICache
from core.domain.exceptions import CacheError
from core.infrastructure.redis_fallback_manager import (
    RedisFallbackManager,
    RedisLimits,
    FallbackStrategy
)
from core.infrastructure.cache import MemoryCache

logger = logging.getLogger(__name__)


class RedisCache(ICache):
    """
    Redis cache with fallback mechanisms for free tier limits

    Features:
    - Connection pooling and rate limiting
    - Memory cache fallback when Redis is unavailable
    - Queue system for retrying failed operations
    - Graceful degradation under load
    - Comprehensive monitoring and health checks
    """

    def __init__(
        self,
        redis_url: str,
        key_prefix: str = "cache:",
        fallback_strategy: FallbackStrategy = FallbackStrategy.HYBRID,
        memory_cache_size: int = 1000,
        memory_cache_ttl: int = 300,
        redis_limits: Optional[RedisLimits] = None
    ):
        self.redis_url = redis_url
        self.key_prefix = key_prefix
        self.fallback_strategy = fallback_strategy

        # Initialize fallback manager
        self.fallback_manager = RedisFallbackManager(
            redis_url=redis_url,
            limits=redis_limits or RedisLimits(),
            fallback_strategy=fallback_strategy,
            enable_memory_fallback=True,
            enable_queue_system=True
        )

        # Memory cache for fallback
        self.memory_cache = MemoryCache(
            max_size=memory_cache_size,
            default_ttl=memory_cache_ttl
        )

        # Initialization flag
        self._initialized = False

    async def initialize(self):
        """Initialize the cache system"""
        if not self._initialized:
            await self.fallback_manager.initialize()
            self._initialized = True
            logger.info("Redis cache initialized")

    def _make_key(self, key: str) -> str:
        """Create prefixed key"""
        return f"{self.key_prefix}{key}"

    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache with fallback"""
        if not self._initialized:
            await self.initialize()

        prefixed_key = self._make_key(key)

        try:
            # Try Redis first with fallback manager
            result = await self.fallback_manager.execute_operation(
                "get",
                prefixed_key,
                fallback_value=None
            )

            if result is not None:
                # Deserialize Redis result
                try:
                    return pickle.loads(result) if isinstance(result, bytes) else result
                except (pickle.PickleError, TypeError):
                    # If deserialization fails, return raw value
                    return result

            # If Redis returns None, try memory cache as secondary fallback
            return await self.memory_cache.get(key)

        except Exception as e:
            logger.warning(f"Cache get operation failed for key {key}: {e}")
            # Final fallback to memory cache
            return await self.memory_cache.get(key)

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache with fallback"""
        if not self._initialized:
            await self.initialize()

        prefixed_key = self._make_key(key)

        try:
            # Serialize value for Redis
            serialized_value = pickle.dumps(value)

            # Try Redis first
            if ttl is not None:
                await self.fallback_manager.execute_operation(
                    "setex",
                    prefixed_key,
                    ttl,
                    serialized_value,
                    fallback_value=True,
                    ttl=ttl,
                    priority=1  # Set operations have priority
                )
            else:
                await self.fallback_manager.execute_operation(
                    "set",
                    prefixed_key,
                    serialized_value,
                    fallback_value=True,
                    ttl=ttl,
                    priority=1
                )

            # Also set in memory cache as backup
            await self.memory_cache.set(key, value, ttl)

        except Exception as e:
            logger.warning(f"Cache set operation failed for key {key}: {e}")
            # Fallback to memory cache only
            await self.memory_cache.set(key, value, ttl)

    async def delete(self, key: str) -> None:
        """Delete value from cache with fallback"""
        if not self._initialized:
            await self.initialize()

        prefixed_key = self._make_key(key)

        try:
            # Try Redis first
            await self.fallback_manager.execute_operation(
                "delete",
                prefixed_key,
                fallback_value=True,
                priority=2  # Delete operations have high priority
            )

        except Exception as e:
            logger.warning(f"Cache delete operation failed for key {key}: {e}")

        # Always delete from memory cache too
        await self.memory_cache.delete(key)

    async def exists(self, key: str) -> bool:
        """Check if key exists with fallback"""
        if not self._initialized:
            await self.initialize()

        prefixed_key = self._make_key(key)

        try:
            # Try Redis first
            result = await self.fallback_manager.execute_operation(
                "exists",
                prefixed_key,
                fallback_value=False
            )

            if result:
                return bool(result)

            # Fallback to memory cache
            return await self.memory_cache.exists(key)

        except Exception as e:
            logger.warning(f"Cache exists operation failed for key {key}: {e}")
            # Final fallback to memory cache
            return await self.memory_cache.exists(key)

    async def clear(self) -> None:
        """Clear all cache with fallback"""
        if not self._initialized:
            await self.initialize()

        try:
            # Try to clear Redis cache
            pattern = f"{self.key_prefix}*"

            # Get keys first
            keys = await self.fallback_manager.execute_operation(
                "keys",
                pattern,
                fallback_value=[]
            )

            # Delete keys if found
            if keys:
                await self.fallback_manager.execute_operation(
                    "delete",
                    *keys,
                    fallback_value=True,
                    priority=3  # Clear operations have highest priority
                )

        except Exception as e:
            logger.warning(f"Cache clear operation failed: {e}")

        # Always clear memory cache
        await self.memory_cache.clear()

    async def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status"""
        if not self._initialized:
            return {
                "status": "not_initialized",
                "redis_healthy": False,
                "memory_cache_size": 0
            }

        # Get fallback manager health
        health = await self.fallback_manager.get_health_status()

        # Add memory cache info
        health.update({
            "memory_cache_size": len(self.memory_cache._cache),
            "memory_cache_max_size": self.memory_cache.max_size,
            "cache_strategy": self.fallback_strategy.value,
            "initialized": self._initialized
        })

        return health

    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get detailed cache statistics"""
        health = await self.get_health_status()

        # Calculate cache efficiency
        total_ops = health.get('operations_per_second', 0)
        success_rate = health.get('success_rate', 0)
        fallback_rate = health.get('fallback_rate', 0)

        return {
            "performance": {
                "operations_per_second": total_ops,
                "success_rate_percent": round(success_rate, 2),
                "fallback_rate_percent": round(fallback_rate, 2),
                "redis_healthy": health.get('redis_healthy', False)
            },
            "capacity": {
                "redis_connections_used": health.get('max_connections', 0) - health.get('available_connections', 0),
                "redis_connections_available": health.get('available_connections', 0),
                "memory_cache_utilization": round(
                    (health.get('memory_cache_size', 0) / max(1, health.get('memory_cache_max_size', 1))) * 100, 2
                ),
                "queue_size": health.get('queue_size', 0)
            },
            "limits": health.get('limits', {}),
            "recommendations": self._get_recommendations(health)
        }

    def _get_recommendations(self, health: Dict[str, Any]) -> List[str]:
        """Get optimization recommendations based on current status"""
        recommendations = []

        success_rate = health.get('success_rate', 100)
        fallback_rate = health.get('fallback_rate', 0)
        queue_size = health.get('queue_size', 0)
        available_connections = health.get('available_connections', 30)

        if success_rate < 80:
            recommendations.append("Redis success rate is low - consider upgrading Redis plan")

        if fallback_rate > 30:
            recommendations.append("High fallback rate - Redis may be overloaded")

        if queue_size > 50:
            recommendations.append("Large operation queue - consider reducing operation frequency")

        if available_connections < 5:
            recommendations.append("Low available connections - optimize connection usage")

        if not health.get('redis_healthy', True):
            recommendations.append("Redis is unhealthy - check connection and credentials")

        if not recommendations:
            recommendations.append("Cache performance is optimal")

        return recommendations

    async def dispose(self) -> None:
        """Dispose cache resources"""
        if self._initialized:
            await self.fallback_manager.dispose()
            await self.memory_cache.clear()
            self._initialized = False
            logger.info("Redis cache disposed")
