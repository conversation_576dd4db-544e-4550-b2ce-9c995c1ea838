"""
Supabase User Store Implementation

This module provides Supabase-backed implementations of IUserStore and IRoleStore
using Supabase Auth + PostgreSQL with Row Level Security (RLS).
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Set
from pydantic import BaseModel

from core.domain.interfaces import IUserStore, IRoleStore
from core.domain.models import User, Role, Permission, UserStatus
from core.infrastructure.supabase_client import SupabaseClientManager

import logging

logger = logging.getLogger(__name__)


class SupabaseUserProfile(BaseModel):
    """Extended user profile stored in Supabase"""
    id: str
    auth_user_id: str  # Links to Supabase Auth user
    username: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    status: str = UserStatus.ACTIVE.value
    is_superuser: bool = False
    is_verified: bool = False
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    metadata: Dict[str, Any] = {}


class SupabaseUserStore(IUserStore):
    """
    Supabase-backed user store with Auth integration
    
    Features:
    - Supabase Auth for authentication
    - PostgreSQL with RLS for user profiles
    - Real-time user updates
    - Social login support
    - Email verification workflows
    """

    def __init__(self, supabase_manager: SupabaseClientManager):
        self.supabase = supabase_manager
        self.table_name = "user_profiles"

    async def create_user(self, user: User) -> User:
        """Create user with Supabase Auth + profile"""
        try:
            # Create user in Supabase Auth
            auth_response = self.supabase.sign_up(
                email=user.email,
                password="temp_password",  # Will be updated via password reset
                options={
                    "data": {
                        "username": user.username,
                        "first_name": user.first_name,
                        "last_name": user.last_name
                    }
                }
            )

            if not auth_response.user:
                raise Exception("Failed to create auth user")

            auth_user_id = auth_response.user.id

            # Create user profile in database
            profile_data = {
                "id": user.id,
                "auth_user_id": auth_user_id,
                "username": user.username,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "status": user.status.value,
                "is_superuser": user.is_superuser,
                "is_verified": user.is_verified,
                "created_at": user.created_at.isoformat(),
                "updated_at": user.updated_at.isoformat(),
                "metadata": {"role_ids": list(user.role_ids)}
            }

            result = self.supabase.table(self.table_name, use_service_key=True).insert(profile_data).execute()

            if not result.data:
                raise Exception("Failed to create user profile")

            # Update user with auth_user_id
            user.metadata = {"auth_user_id": auth_user_id}
            
            logger.info(f"Created user: {user.username} with Supabase Auth")
            return user

        except Exception as e:
            logger.error(f"Failed to create user {user.username}: {e}")
            raise

    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        try:
            result = self.supabase.table(self.table_name).select("*").eq("id", user_id).single().execute()
            
            if not result.data:
                return None

            return self._profile_to_user(result.data)

        except Exception as e:
            logger.error(f"Failed to get user by ID {user_id}: {e}")
            return None

    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        try:
            result = self.supabase.table(self.table_name).select("*").eq("username", username).single().execute()
            
            if not result.data:
                return None

            return self._profile_to_user(result.data)

        except Exception as e:
            logger.error(f"Failed to get user by username {username}: {e}")
            return None

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email (requires auth lookup)"""
        try:
            # Get user from Supabase Auth by email
            # Note: This requires admin privileges
            auth_users = self.supabase.service_client.auth.admin.list_users()
            auth_user = next((u for u in auth_users if u.email == email), None)
            
            if not auth_user:
                return None

            # Get profile by auth_user_id
            result = self.supabase.table(self.table_name).select("*").eq("auth_user_id", auth_user.id).single().execute()
            
            if not result.data:
                return None

            user = self._profile_to_user(result.data)
            user.email = email  # Set email from auth
            return user

        except Exception as e:
            logger.error(f"Failed to get user by email {email}: {e}")
            return None

    async def update_user(self, user: User) -> User:
        """Update user profile"""
        try:
            update_data = {
                "username": user.username,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "status": user.status.value,
                "is_superuser": user.is_superuser,
                "is_verified": user.is_verified,
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "metadata": {"role_ids": list(user.role_ids)}
            }

            result = self.supabase.table(self.table_name, use_service_key=True).update(update_data).eq("id", user.id).execute()

            if not result.data:
                raise Exception("Failed to update user profile")

            logger.info(f"Updated user: {user.username}")
            return user

        except Exception as e:
            logger.error(f"Failed to update user {user.id}: {e}")
            raise

    async def delete_user(self, user_id: str) -> bool:
        """Delete user (soft delete by setting status)"""
        try:
            # Soft delete by updating status
            update_data = {
                "status": UserStatus.INACTIVE.value,
                "updated_at": datetime.now(timezone.utc).isoformat()
            }

            result = self.supabase.table(self.table_name, use_service_key=True).update(update_data).eq("id", user_id).execute()

            success = bool(result.data)
            if success:
                logger.info(f"Soft deleted user: {user_id}")
            
            return success

        except Exception as e:
            logger.error(f"Failed to delete user {user_id}: {e}")
            return False

    async def list_users(self, limit: int = 100, offset: int = 0, status: Optional[UserStatus] = None) -> List[User]:
        """List users with pagination"""
        try:
            query = self.supabase.table(self.table_name).select("*")
            
            if status:
                query = query.eq("status", status.value)
            
            result = query.range(offset, offset + limit - 1).execute()
            
            users = []
            for profile_data in result.data:
                user = self._profile_to_user(profile_data)
                if user:
                    users.append(user)
            
            return users

        except Exception as e:
            logger.error(f"Failed to list users: {e}")
            return []

    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """Authenticate user with Supabase Auth"""
        try:
            # Sign in with Supabase Auth
            auth_response = self.supabase.sign_in(email, password)
            
            if not auth_response.user:
                return None

            # Get user profile
            result = self.supabase.table(self.table_name).select("*").eq("auth_user_id", auth_response.user.id).single().execute()
            
            if not result.data:
                return None

            user = self._profile_to_user(result.data)
            user.email = email
            
            # Update last login
            await self._update_last_login(user.id)
            
            logger.info(f"Authenticated user: {user.username}")
            return user

        except Exception as e:
            logger.error(f"Failed to authenticate user {email}: {e}")
            return None

    async def change_password(self, user_id: str, new_password: str) -> bool:
        """Change user password via Supabase Auth"""
        try:
            # Get user profile to find auth_user_id
            user = await self.get_user_by_id(user_id)
            if not user or not user.metadata.get("auth_user_id"):
                return False

            auth_user_id = user.metadata["auth_user_id"]
            
            # Update password via Supabase Auth Admin API
            self.supabase.service_client.auth.admin.update_user_by_id(
                auth_user_id,
                {"password": new_password}
            )
            
            logger.info(f"Changed password for user: {user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to change password for user {user_id}: {e}")
            return False

    async def _update_last_login(self, user_id: str) -> None:
        """Update last login timestamp"""
        try:
            update_data = {
                "last_login": datetime.now(timezone.utc).isoformat(),
                "updated_at": datetime.now(timezone.utc).isoformat()
            }

            self.supabase.table(self.table_name, use_service_key=True).update(update_data).eq("id", user_id).execute()

        except Exception as e:
            logger.warning(f"Failed to update last login for user {user_id}: {e}")

    def _profile_to_user(self, profile_data: Dict[str, Any]) -> Optional[User]:
        """Convert Supabase profile data to User domain model"""
        try:
            role_ids = set()
            if profile_data.get("metadata") and isinstance(profile_data["metadata"], dict):
                role_ids = set(profile_data["metadata"].get("role_ids", []))

            return User(
                id=profile_data["id"],
                username=profile_data["username"],
                email="",  # Will be set from auth if needed
                password_hash="",  # Managed by Supabase Auth
                first_name=profile_data.get("first_name"),
                last_name=profile_data.get("last_name"),
                status=UserStatus(profile_data.get("status", UserStatus.ACTIVE.value)),
                is_superuser=profile_data.get("is_superuser", False),
                is_verified=profile_data.get("is_verified", False),
                last_login=datetime.fromisoformat(profile_data["last_login"]) if profile_data.get("last_login") else None,
                created_at=datetime.fromisoformat(profile_data["created_at"]),
                updated_at=datetime.fromisoformat(profile_data["updated_at"]),
                role_ids=role_ids,
                metadata={"auth_user_id": profile_data.get("auth_user_id")}
            )

        except Exception as e:
            logger.error(f"Failed to convert profile data to user: {e}")
            return None


class SupabaseRoleStore(IRoleStore):
    """
    Supabase-backed role store with RLS
    
    Features:
    - PostgreSQL with RLS for roles
    - Real-time role updates
    - Permission management
    """

    def __init__(self, supabase_manager: SupabaseClientManager):
        self.supabase = supabase_manager
        self.table_name = "roles"

    async def create_role(self, role: Role) -> Role:
        """Create role"""
        try:
            role_data = {
                "id": role.id,
                "name": role.name,
                "description": role.description,
                "is_system_role": role.is_system_role,
                "created_at": role.created_at.isoformat(),
                "updated_at": role.updated_at.isoformat(),
                "metadata": {"permission_ids": list(role.permission_ids)}
            }

            result = self.supabase.table(self.table_name, use_service_key=True).insert(role_data).execute()

            if not result.data:
                raise Exception("Failed to create role")

            logger.info(f"Created role: {role.name}")
            return role

        except Exception as e:
            logger.error(f"Failed to create role {role.name}: {e}")
            raise

    async def get_role_by_id(self, role_id: str) -> Optional[Role]:
        """Get role by ID"""
        try:
            result = self.supabase.table(self.table_name).select("*").eq("id", role_id).single().execute()
            
            if not result.data:
                return None

            return self._data_to_role(result.data)

        except Exception as e:
            logger.error(f"Failed to get role by ID {role_id}: {e}")
            return None

    async def get_role_by_name(self, name: str) -> Optional[Role]:
        """Get role by name"""
        try:
            result = self.supabase.table(self.table_name).select("*").eq("name", name).single().execute()
            
            if not result.data:
                return None

            return self._data_to_role(result.data)

        except Exception as e:
            logger.error(f"Failed to get role by name {name}: {e}")
            return None

    async def list_roles(self, limit: int = 100, offset: int = 0) -> List[Role]:
        """List roles with pagination"""
        try:
            result = self.supabase.table(self.table_name).select("*").range(offset, offset + limit - 1).execute()
            
            roles = []
            for role_data in result.data:
                role = self._data_to_role(role_data)
                if role:
                    roles.append(role)
            
            return roles

        except Exception as e:
            logger.error(f"Failed to list roles: {e}")
            return []

    async def update_role(self, role: Role) -> Role:
        """Update role"""
        try:
            update_data = {
                "name": role.name,
                "description": role.description,
                "is_system_role": role.is_system_role,
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "metadata": {"permission_ids": list(role.permission_ids)}
            }

            result = self.supabase.table(self.table_name, use_service_key=True).update(update_data).eq("id", role.id).execute()

            if not result.data:
                raise Exception("Failed to update role")

            logger.info(f"Updated role: {role.name}")
            return role

        except Exception as e:
            logger.error(f"Failed to update role {role.id}: {e}")
            raise

    async def delete_role(self, role_id: str) -> bool:
        """Delete role"""
        try:
            result = self.supabase.table(self.table_name, use_service_key=True).delete().eq("id", role_id).execute()

            success = bool(result.data)
            if success:
                logger.info(f"Deleted role: {role_id}")
            
            return success

        except Exception as e:
            logger.error(f"Failed to delete role {role_id}: {e}")
            return False

    def _data_to_role(self, role_data: Dict[str, Any]) -> Optional[Role]:
        """Convert Supabase data to Role domain model"""
        try:
            permission_ids = set()
            if role_data.get("metadata") and isinstance(role_data["metadata"], dict):
                permission_ids = set(role_data["metadata"].get("permission_ids", []))

            return Role(
                id=role_data["id"],
                name=role_data["name"],
                description=role_data.get("description"),
                is_system_role=role_data.get("is_system_role", False),
                created_at=datetime.fromisoformat(role_data["created_at"]),
                updated_at=datetime.fromisoformat(role_data["updated_at"]),
                permission_ids=permission_ids
            )

        except Exception as e:
            logger.error(f"Failed to convert role data: {e}")
            return None
