#!/usr/bin/env python3
"""
Simple Redis Connection Test

Test Redis connection using the exact method from your example.
"""

import asyncio
import os


async def test_redis_simple():
    """Test Redis using the exact connection method from your example"""
    print("🔧 Simple Redis Connection Test")
    print("=" * 50)
    
    try:
        # Test with synchronous redis first (like your example)
        print("1. Testing synchronous Redis connection...")
        import redis
        
        r = redis.Redis(
            host='memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com',
            port=10401,
            decode_responses=True,
            username="default",
            password="null",
        )
        
        # Test the exact operations from your example
        success = r.set('foo', 'bar')
        print(f"   Set operation result: {success}")
        
        result = r.get('foo')
        print(f"   Get operation result: {result}")
        
        if result == 'bar':
            print("   ✅ Synchronous Redis connection working!")
        else:
            print("   ❌ Synchronous Redis connection failed")
            return False
            
        # Test some additional operations
        r.set('test_key', 'test_value', ex=30)
        test_result = r.get('test_key')
        print(f"   Additional test result: {test_result}")
        
        # Get Redis info
        info = r.info()
        print(f"   Redis version: {info.get('redis_version', 'unknown')}")
        print(f"   Connected clients: {info.get('connected_clients', 'unknown')}")
        
        # Cleanup
        r.delete('foo', 'test_key')
        r.close()
        
    except Exception as e:
        print(f"   ❌ Synchronous Redis failed: {e}")
        return False
    
    try:
        # Test with async redis
        print("\n2. Testing async Redis connection...")
        import redis.asyncio as aioredis
        
        # Try different connection methods for async
        connection_methods = [
            {
                "name": "URL with username/password",
                "method": lambda: aioredis.from_url(
                    "redis://default:<EMAIL>:10401",
                    decode_responses=True
                )
            },
            {
                "name": "Direct connection with auth",
                "method": lambda: aioredis.Redis(
                    host='memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com',
                    port=10401,
                    decode_responses=True,
                    username="default",
                    password="null"
                )
            },
            {
                "name": "Connection pool",
                "method": lambda: aioredis.ConnectionPool.from_url(
                    "redis://default:<EMAIL>:10401",
                    decode_responses=True
                )
            }
        ]
        
        for method_info in connection_methods:
            print(f"\n   Testing: {method_info['name']}")
            try:
                if method_info['name'] == "Connection pool":
                    pool = method_info['method']()
                    redis_client = aioredis.Redis(connection_pool=pool)
                else:
                    redis_client = method_info['method']()
                
                # Test ping
                pong = await redis_client.ping()
                print(f"      Ping result: {pong}")
                
                # Test set/get
                await redis_client.set('async_test', 'async_value', ex=30)
                value = await redis_client.get('async_test')
                print(f"      Set/Get result: {value}")
                
                if value == 'async_value':
                    print(f"      ✅ {method_info['name']} working!")
                    
                    # Cleanup and close
                    await redis_client.delete('async_test')
                    await redis_client.close()
                    return True
                else:
                    print(f"      ❌ {method_info['name']} failed")
                    await redis_client.close()
                    
            except Exception as e:
                print(f"      ❌ {method_info['name']} failed: {e}")
        
        return False
        
    except Exception as e:
        print(f"   ❌ Async Redis failed: {e}")
        return False


async def test_redis_info_detailed():
    """Get detailed Redis information"""
    print("\n3. Getting detailed Redis information...")
    
    try:
        import redis
        
        r = redis.Redis(
            host='memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com',
            port=10401,
            decode_responses=True,
            username="default",
            password="null",
        )
        
        # Get all info sections
        info_sections = ['server', 'clients', 'memory', 'persistence', 'stats', 'replication', 'cpu', 'commandstats', 'cluster', 'keyspace']
        
        for section in info_sections:
            try:
                info = r.info(section)
                if info:
                    print(f"\n   {section.upper()} INFO:")
                    for key, value in list(info.items())[:5]:  # Show first 5 items
                        print(f"      {key}: {value}")
            except Exception as e:
                print(f"   {section}: {e}")
        
        r.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Failed to get Redis info: {e}")
        return False


async def main():
    """Main test function"""
    print("🚀 Simple Redis Connection Test Suite")
    print("=" * 60)
    
    # Test basic connection
    basic_success = await test_redis_simple()
    
    if basic_success:
        # Get detailed info
        await test_redis_info_detailed()
        
        print(f"\n🎉 Redis connection successful!")
        print(f"✅ Your Redis instance is working correctly")
        print(f"✅ Both sync and async connections work")
        print(f"\nYou can now update your application to use:")
        print(f"   Host: memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com")
        print(f"   Port: 10401")
        print(f"   Username: default")
        print(f"   Password: null")
        
    else:
        print(f"\n❌ Redis connection failed")
        print(f"Please check:")
        print(f"   • Redis instance is running")
        print(f"   • Network connectivity")
        print(f"   • Authentication credentials")
    
    return basic_success


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
