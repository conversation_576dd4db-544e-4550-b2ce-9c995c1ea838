"""
Metrics Implementations

This module provides concrete implementations of the IMetricsCollector interface
including Prometheus metrics collection.
"""

import time
from typing import Dict, Optional, Any
from threading import Lock
from datetime import datetime

from core.domain.interfaces import IMetricsCollector
from core.domain.exceptions import DomainException


class PrometheusMetricsCollector(IMetricsCollector):
    """Prometheus metrics collector implementation"""

    def __init__(self, port: int = 9090, path: str = "/metrics"):
        self.port = port
        self.path = path
        self._counters = {}
        self._gauges = {}
        self._histograms = {}
        self._timers = {}
        self._lock = Lock()
        self._prometheus_available = False

        # Try to import prometheus_client
        try:
            from prometheus_client import Counter, Gauge, Histogram, Summary, start_http_server
            self._Counter = Counter
            self._Gauge = Gauge
            self._Histogram = Histogram
            self._Summary = Summary
            self._start_http_server = start_http_server
            self._prometheus_available = True
        except ImportError:
            pass

    def _ensure_prometheus(self) -> None:
        """Ensure Prometheus is available"""
        if not self._prometheus_available:
            raise DomainException(
                "Prometheus client not available. Install with: pip install prometheus-client",
                "PROMETHEUS_UNAVAILABLE"
            )

    def _get_or_create_counter(self, name: str, labels: Optional[Dict[str, str]] = None):
        """Get or create a Prometheus counter"""
        self._ensure_prometheus()

        with self._lock:
            if name not in self._counters:
                label_names = list(labels.keys()) if labels else []
                self._counters[name] = self._Counter(name, name, label_names)

            counter = self._counters[name]
            return counter.labels(**labels) if labels else counter

    def _get_or_create_gauge(self, name: str, labels: Optional[Dict[str, str]] = None):
        """Get or create a Prometheus gauge"""
        self._ensure_prometheus()

        with self._lock:
            if name not in self._gauges:
                label_names = list(labels.keys()) if labels else []
                self._gauges[name] = self._Gauge(name, name, label_names)

            gauge = self._gauges[name]
            return gauge.labels(**labels) if labels else gauge

    def _get_or_create_histogram(self, name: str, labels: Optional[Dict[str, str]] = None):
        """Get or create a Prometheus histogram"""
        self._ensure_prometheus()

        with self._lock:
            if name not in self._histograms:
                label_names = list(labels.keys()) if labels else []
                self._histograms[name] = self._Histogram(name, name, label_names)

            histogram = self._histograms[name]
            return histogram.labels(**labels) if labels else histogram

    def _get_or_create_timer(self, name: str, labels: Optional[Dict[str, str]] = None):
        """Get or create a Prometheus summary for timing"""
        self._ensure_prometheus()

        with self._lock:
            if name not in self._timers:
                label_names = list(labels.keys()) if labels else []
                self._timers[name] = self._Summary(name, name, label_names)

            timer = self._timers[name]
            return timer.labels(**labels) if labels else timer

    def increment_counter(self, name: str, tags: Optional[Dict[str, str]] = None) -> None:
        """Increment counter metric"""
        try:
            counter = self._get_or_create_counter(name, tags)
            counter.inc()
        except Exception as e:
            # Log error but don't break application
            print(f"Error incrementing counter {name}: {e}")

    def record_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record gauge metric"""
        try:
            gauge = self._get_or_create_gauge(name, tags)
            gauge.set(value)
        except Exception as e:
            # Log error but don't break application
            print(f"Error recording gauge {name}: {e}")

    def record_histogram(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record histogram metric"""
        try:
            histogram = self._get_or_create_histogram(name, tags)
            histogram.observe(value)
        except Exception as e:
            # Log error but don't break application
            print(f"Error recording histogram {name}: {e}")

    def record_timer(self, name: str, duration: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record timer metric"""
        try:
            timer = self._get_or_create_timer(name, tags)
            timer.observe(duration)
        except Exception as e:
            # Log error but don't break application
            print(f"Error recording timer {name}: {e}")

    def start_server(self) -> None:
        """Start Prometheus metrics server"""
        if self._prometheus_available:
            try:
                self._start_http_server(self.port)
                print(f"📊 Prometheus metrics server started on port {self.port}")
            except Exception as e:
                print(f"Failed to start metrics server: {e}")

    async def get_metrics(self) -> Dict[str, Any]:
        """Get all collected metrics in a format compatible with the interface"""
        # For Prometheus, we return a summary of registered metrics
        # The actual metrics are served via the HTTP endpoint
        with self._lock:
            return {
                "counters": list(self._counters.keys()),
                "gauges": list(self._gauges.keys()),
                "histograms": list(self._histograms.keys()),
                "timers": list(self._timers.keys()),
                "timestamp": datetime.now().isoformat(),
                "metrics_endpoint": f"http://localhost:{self.port}{self.path}",
                "prometheus_available": self._prometheus_available
            }


class NoOpMetricsCollector(IMetricsCollector):
    """No-operation metrics collector for testing or when metrics are disabled"""

    def increment_counter(self, name: str, tags: Optional[Dict[str, str]] = None) -> None:
        """No-op counter increment"""
        pass

    def record_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """No-op gauge recording"""
        pass

    def record_histogram(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """No-op histogram recording"""
        pass

    def record_timer(self, name: str, duration: float, tags: Optional[Dict[str, str]] = None) -> None:
        """No-op timer recording"""
        pass

    async def get_metrics(self) -> Dict[str, Any]:
        """No-op metrics retrieval"""
        return {
            "counters": {},
            "gauges": {},
            "histograms": {},
            "timers": {},
            "timestamp": datetime.now().isoformat()
        }


class InMemoryMetricsCollector(IMetricsCollector):
    """In-memory metrics collector for testing"""

    def __init__(self):
        self.counters: Dict[str, float] = {}
        self.gauges: Dict[str, float] = {}
        self.histograms: Dict[str, list] = {}
        self.timers: Dict[str, list] = {}
        self._lock = Lock()

    def _make_key(self, name: str, tags: Optional[Dict[str, str]] = None) -> str:
        """Create metric key with tags"""
        if not tags:
            return name
        tag_str = ",".join(f"{k}={v}" for k, v in sorted(tags.items()))
        return f"{name}[{tag_str}]"

    def increment_counter(self, name: str, tags: Optional[Dict[str, str]] = None, value: float = 1) -> None:
        """Increment counter metric"""
        key = self._make_key(name, tags)
        with self._lock:
            self.counters[key] = self.counters.get(key, 0) + value

    def record_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record gauge metric"""
        key = self._make_key(name, tags)
        with self._lock:
            self.gauges[key] = value

    def set_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Set gauge metric (alias for record_gauge)"""
        self.record_gauge(name, value, tags)

    def record_histogram(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record histogram metric"""
        key = self._make_key(name, tags)
        with self._lock:
            if key not in self.histograms:
                self.histograms[key] = []
            self.histograms[key].append(value)

    def record_timer(self, name: str, duration: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record timer metric"""
        key = self._make_key(name, tags)
        with self._lock:
            if key not in self.timers:
                self.timers[key] = []
            self.timers[key].append(duration)

    def get_counter_value(self, name: str, tags: Optional[Dict[str, str]] = None) -> float:
        """Get counter value for testing"""
        key = self._make_key(name, tags)
        return self.counters.get(key, 0)

    def get_gauge_value(self, name: str, tags: Optional[Dict[str, str]] = None) -> Optional[float]:
        """Get gauge value for testing"""
        key = self._make_key(name, tags)
        return self.gauges.get(key)

    def get_histogram_values(self, name: str, tags: Optional[Dict[str, str]] = None) -> list:
        """Get histogram values for testing"""
        key = self._make_key(name, tags)
        return self.histograms.get(key, [])

    def get_timer_values(self, name: str, tags: Optional[Dict[str, str]] = None) -> list:
        """Get timer values for testing"""
        key = self._make_key(name, tags)
        return self.timers.get(key, [])

    async def get_metrics(self) -> Dict[str, Any]:
        """Get all collected metrics"""
        with self._lock:
            # Format histograms with statistical data
            formatted_histograms = {}
            for key, values in self.histograms.items():
                if values:
                    formatted_histograms[key] = {
                        "count": len(values),
                        "sum": sum(values),
                        "avg": sum(values) / len(values),
                        "min": min(values),
                        "max": max(values)
                    }
                else:
                    formatted_histograms[key] = {
                        "count": 0,
                        "sum": 0.0,
                        "avg": 0.0,
                        "min": 0.0,
                        "max": 0.0
                    }

            # Format timers with statistical data
            formatted_timers = {}
            for key, values in self.timers.items():
                if values:
                    formatted_timers[key] = {
                        "count": len(values),
                        "sum": sum(values),
                        "avg": sum(values) / len(values),
                        "min": min(values),
                        "max": max(values)
                    }
                else:
                    formatted_timers[key] = {
                        "count": 0,
                        "sum": 0.0,
                        "avg": 0.0,
                        "min": 0.0,
                        "max": 0.0
                    }

            return {
                "counters": self.counters.copy(),
                "gauges": self.gauges.copy(),
                "histograms": formatted_histograms,
                "timers": formatted_timers,
                "timestamp": datetime.now().isoformat()
            }

    def clear(self) -> None:
        """Clear all metrics"""
        with self._lock:
            self.counters.clear()
            self.gauges.clear()
            self.histograms.clear()
            self.timers.clear()