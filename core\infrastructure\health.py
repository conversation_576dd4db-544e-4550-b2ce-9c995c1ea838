"""
Health Check Implementation

This module provides concrete implementations of the IHealthCheck interface
for monitoring system health and status.
"""

import time
import psutil
import shutil
from typing import Dict, Any, Optional
from datetime import datetime, timezone

from core.domain.interfaces import IHealthCheck


class SystemHealthCheck(IHealthCheck):
    """System health check implementation"""

    def __init__(
        self,
        app_name: str = "Core System",
        version: str = "1.0.0",
        memory_threshold: float = 80.0,  # Memory usage threshold in %
        disk_threshold: float = 85.0,    # Disk usage threshold in %
        database_url: Optional[str] = None,
        redis_url: Optional[str] = None
    ):
        self._name = app_name
        self._version = version
        self._start_time = time.time()
        self._memory_threshold = memory_threshold
        self._disk_threshold = disk_threshold
        self._database_url = database_url
        self._redis_url = redis_url

    @property
    def name(self) -> str:
        """Health check name"""
        return self._name

    async def check_health(self) -> Dict[str, Any]:
        """Perform health check and return status"""
        uptime = time.time() - self._start_time

        # Perform all health checks
        checks = {
            "database": await self._check_database(),
            "cache": await self._check_cache(),
            "memory": await self._check_memory(),
            "disk": await self._check_disk()
        }

        # Determine overall status
        # Only consider "unhealthy" checks as failures, "skipped" checks are OK
        overall_status = "healthy"
        for check_name, check_result in checks.items():
            if check_result["status"] == "unhealthy":
                overall_status = "unhealthy"
                break

        return {
            "status": overall_status,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "version": self._version,
            "uptime": uptime,
            "service": self._name,
            "checks": checks
        }

    async def check_readiness(self) -> Dict[str, Any]:
        """Check if service is ready to accept requests"""
        return {
            "status": "ready",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "service": self._name,
            "version": self._version
        }

    async def check_liveness(self) -> Dict[str, Any]:
        """Check if service is alive"""
        return {
            "status": "alive",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "service": self._name,
            "version": self._version
        }

    async def _check_database(self) -> Dict[str, Any]:
        """Check database connectivity"""
        if not self._database_url:
            return {
                "status": "skipped",
                "message": "No database URL configured",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        try:
            # Try to import and test database connection
            import asyncpg

            # Parse connection string and test connection
            conn = await asyncpg.connect(self._database_url)
            await conn.execute("SELECT 1")
            await conn.close()

            return {
                "status": "healthy",
                "message": "Database connection successful",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        except ImportError:
            return {
                "status": "skipped",
                "message": "Database driver not available",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "message": f"Database connection failed: {str(e)}",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    async def _check_cache(self) -> Dict[str, Any]:
        """Check Redis cache connectivity"""
        if not self._redis_url:
            return {
                "status": "skipped",
                "message": "No Redis URL configured",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        try:
            import redis.asyncio as redis

            # Test Redis connection
            client = redis.from_url(self._redis_url)
            await client.ping()
            await client.close()

            return {
                "status": "healthy",
                "message": "Redis connection successful",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        except ImportError:
            return {
                "status": "skipped",
                "message": "Redis driver not available",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "message": f"Redis connection failed: {str(e)}",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    async def _check_memory(self) -> Dict[str, Any]:
        """Check system memory usage"""
        try:
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            status = "healthy" if memory_percent < self._memory_threshold else "unhealthy"

            return {
                "status": status,
                "message": f"Memory usage: {memory_percent:.1f}% (threshold: {self._memory_threshold}%)",
                "details": {
                    "used_percent": memory_percent,
                    "used_gb": round(memory.used / (1024**3), 2),
                    "total_gb": round(memory.total / (1024**3), 2),
                    "available_gb": round(memory.available / (1024**3), 2),
                    "threshold_percent": self._memory_threshold
                },
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "message": f"Memory check failed: {str(e)}",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    async def _check_disk(self) -> Dict[str, Any]:
        """Check disk space usage"""
        try:
            # Check disk usage for the current directory (where the app is running)
            disk_usage = shutil.disk_usage(".")
            total_space = disk_usage.total
            free_space = disk_usage.free
            used_space = total_space - free_space
            used_percent = (used_space / total_space) * 100

            status = "healthy" if used_percent < self._disk_threshold else "unhealthy"

            return {
                "status": status,
                "message": f"Disk usage: {used_percent:.1f}% (threshold: {self._disk_threshold}%)",
                "details": {
                    "used_percent": round(used_percent, 1),
                    "used_gb": round(used_space / (1024**3), 2),
                    "total_gb": round(total_space / (1024**3), 2),
                    "free_gb": round(free_space / (1024**3), 2),
                    "threshold_percent": self._disk_threshold
                },
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "message": f"Disk check failed: {str(e)}",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }