"""
Application Factory

This module provides the main factory for creating and configuring
FastAPI applications with all necessary components and plugins.
"""

import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator, List, Optional
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from core.config.settings import Settings, Environment
from core.infrastructure.container import Container
from core.domain.interfaces import IPlugin, IHealthCheck, IServiceRegistry
from core.application.plugins import PluginManager
from core.infrastructure.cache import MemoryCache
from core.infrastructure.metrics import PrometheusMetricsCollector
from core.infrastructure.security import JWTSecurityProvider


class ApplicationFactory:
    """Factory for creating and configuring FastAPI applications"""

    def __init__(self, settings: Settings):
        self.settings = settings
        self.container = Container()
        self.plugin_manager = PluginManager(self.container)

    def create_app(self) -> FastAPI:
        """Create and configure the FastAPI application"""

        @asynccontextmanager
        async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
            """Application lifespan events"""
            # Startup
            print(f"🚀 Starting {self.settings.APP_NAME} v{self.settings.APP_VERSION}...")

            # Configure dependency injection
            self._configure_services()

            # Initialize plugins
            await self.plugin_manager.load_plugins()

            # Store services in app state
            app.state.container = self.container
            app.state.plugin_manager = self.plugin_manager
            app.state.settings = self.settings

            yield

            # Shutdown
            print(f"👋 Shutting down {self.settings.APP_NAME}...")
            await self.plugin_manager.unload_plugins()
            await self.container.dispose()

        # Create FastAPI app
        app = FastAPI(
            title=self.settings.APP_NAME,
            version=self.settings.APP_VERSION,
            description=self.settings.app.app_description,
            lifespan=lifespan,
            docs_url=self.settings.app.docs_url if self.settings.app.enable_swagger else None,
            redoc_url=self.settings.app.redoc_url if self.settings.app.enable_swagger else None,
            openapi_url=self.settings.app.openapi_url if self.settings.app.enable_swagger else None,
        )

        # Configure middleware
        self._configure_middleware(app)

        # Configure routes
        self._configure_routes(app)

        # Configure exception handlers
        self._configure_exception_handlers(app)

        return app

    def _configure_services(self) -> None:
        """Configure dependency injection services"""
        # Register core services
        from core.domain.interfaces import ICache, IMetricsCollector, ISecurityProvider

        # Cache service with fallback mechanisms
        if self.settings.cache.redis_url:
            from core.infrastructure.redis_cache import RedisCache
            from core.infrastructure.redis_fallback_manager import RedisLimits, FallbackStrategy

            self.container.register_singleton(
                ICache,
                factory=lambda: RedisCache(
                    redis_url=str(self.settings.cache.redis_url),
                    key_prefix=self.settings.cache.cache_key_prefix,
                    fallback_strategy=FallbackStrategy.HYBRID,
                    memory_cache_size=self.settings.cache.local_cache_size,
                    memory_cache_ttl=self.settings.cache.local_cache_ttl,
                    redis_limits=RedisLimits(
                        max_connections=25,  # Conservative for Redis Cloud free tier
                        max_ops_per_second=80,  # Conservative for Redis Cloud free tier
                        max_memory_mb=28  # Conservative for Redis Cloud free tier
                    )
                )
            )
        else:
            self.container.register_singleton(
                ICache,
                MemoryCache,
                factory=lambda: MemoryCache(
                    max_size=self.settings.cache.local_cache_size,
                    default_ttl=self.settings.cache.local_cache_ttl
                )
            )

        # Metrics service
        from core.infrastructure.metrics import InMemoryMetricsCollector, PrometheusMetricsCollector

        # Use Prometheus for production, InMemory for development
        if self.settings.app.environment == Environment.PRODUCTION:
            self.container.register_singleton(
                IMetricsCollector,
                PrometheusMetricsCollector,
                factory=lambda: PrometheusMetricsCollector(
                    port=self.settings.monitoring.metrics_port,
                    path=self.settings.monitoring.metrics_path
                )
            )
        else:
            self.container.register_singleton(
                IMetricsCollector,
                InMemoryMetricsCollector,
                factory=lambda: InMemoryMetricsCollector()
            )

        # Security service - use DatabaseSecurityProvider if database is configured
        from core.infrastructure.simple_supabase import get_database_url_for_sqlalchemy

        # Get database URL - Supabase if available, fallback to regular PostgreSQL
        database_url = get_database_url_for_sqlalchemy(
            self.settings.supabase,
            str(self.settings.database.database_url) if self.settings.database.database_url else None
        )

        if database_url:
            from core.infrastructure.security import DatabaseSecurityProvider
            from core.infrastructure.user_store import DatabaseUserStore, DatabaseRoleStore
            from core.domain.interfaces import IUserStore, IRoleStore

            # Register user and role stores (works with both regular PostgreSQL and Supabase)
            self.container.register_singleton(
                IUserStore,
                DatabaseUserStore,
                factory=lambda: DatabaseUserStore(database_url)
            )
            self.container.register_singleton(
                IRoleStore,
                DatabaseRoleStore,
                factory=lambda: DatabaseRoleStore(database_url)
            )

            # Register database security provider
            self.container.register_singleton(
                ISecurityProvider,
                DatabaseSecurityProvider,
                factory=lambda: DatabaseSecurityProvider(
                    user_store=self.container.get_service(IUserStore),
                    role_store=self.container.get_service(IRoleStore),
                    secret_key=self.settings.get_jwt_secret_key(),
                    algorithm=self.settings.security.jwt_algorithm,
                    access_token_expire_minutes=self.settings.security.access_token_expire_minutes,
                    refresh_token_expire_days=self.settings.security.refresh_token_expire_days
                )
            )
        else:
            # Fallback to JWT security provider for development
            self.container.register_singleton(
                ISecurityProvider,
                JWTSecurityProvider,
                factory=lambda: JWTSecurityProvider(
                    secret_key=self.settings.get_jwt_secret_key(),
                    algorithm=self.settings.security.jwt_algorithm,
                    access_token_expire_minutes=self.settings.security.access_token_expire_minutes
                )
            )

        # Service registry with fallback mechanisms
        if self.settings.cache.redis_url:
            from core.infrastructure.redis_service_registry_v2 import RedisServiceRegistryV2
            from core.infrastructure.redis_fallback_manager import RedisLimits, FallbackStrategy

            self.container.register_singleton(
                IServiceRegistry,
                RedisServiceRegistryV2,
                factory=lambda: RedisServiceRegistryV2(
                    redis_url=str(self.settings.cache.redis_url),
                    key_prefix=f"{self.settings.cache.cache_key_prefix}:service_registry:",
                    default_ttl=300,  # 5 minutes
                    health_check_interval=30,  # 30 seconds
                    fallback_strategy=FallbackStrategy.HYBRID,
                    redis_limits=RedisLimits(
                        max_connections=20,  # Conservative for Redis Cloud free tier
                        max_ops_per_second=60,  # Conservative for Redis Cloud free tier
                        max_memory_mb=25  # Conservative for Redis Cloud free tier
                    )
                )
            )
        else:
            from core.infrastructure.service_registry import ServiceRegistryImpl
            self.container.register_singleton(
                IServiceRegistry,
                ServiceRegistryImpl,
                factory=lambda: ServiceRegistryImpl()
            )

        # Health check service
        from core.infrastructure.health import SystemHealthCheck
        self.container.register_singleton(
            IHealthCheck,
            SystemHealthCheck,
            factory=lambda: SystemHealthCheck(
                app_name=self.settings.APP_NAME,
                version=self.settings.APP_VERSION
            )
        )

        # Simple Supabase client (optional)
        from core.infrastructure.simple_supabase import SimpleSupabaseClient
        self.container.register_singleton(
            SimpleSupabaseClient,
            factory=lambda: SimpleSupabaseClient(self.settings.supabase)
        )

        # Register settings
        self.container.register_singleton(Settings, instance=self.settings)

    def _configure_middleware(self, app: FastAPI) -> None:
        """Configure FastAPI middleware"""
        # CORS middleware
        if self.settings.security.enable_cors:
            app.add_middleware(
                CORSMiddleware,
                allow_origins=self.settings.ALLOWED_ORIGINS,
                allow_credentials=True,
                allow_methods=self.settings.security.cors_methods,
                allow_headers=self.settings.security.cors_headers,
            )

        # Trusted hosts middleware
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=self.settings.ALLOWED_HOSTS
        )

        # Import and add custom middleware
        try:
            from core.presentation.middleware import (
                SecurityHeadersMiddleware,
                RateLimitMiddleware,
                RequestLoggingMiddleware
            )

            app.add_middleware(SecurityHeadersMiddleware)
            app.add_middleware(
                RateLimitMiddleware,
                requests_per_minute=self.settings.RATE_LIMIT
            )
            app.add_middleware(RequestLoggingMiddleware)
        except ImportError:
            # Middleware modules will be created later
            pass

    def _configure_routes(self, app: FastAPI) -> None:
        """Configure FastAPI routes"""
        # Core API routes
        try:
            from core.presentation.api import router as core_router
            app.include_router(core_router, prefix=self.settings.app.api_prefix)
        except ImportError:
            # API module will be created later
            pass

        # Plugin routes will be added by the plugin manager

    def _configure_exception_handlers(self, app: FastAPI) -> None:
        """Configure FastAPI exception handlers"""
        try:
            from core.presentation.exceptions import setup_exception_handlers
            setup_exception_handlers(app)
        except ImportError:
            # Exception handlers will be created later
            pass


def create_application(settings: Optional[Settings] = None) -> FastAPI:
    """Create a FastAPI application with the modular architecture"""
    if settings is None:
        from core.config.settings import settings as default_settings
        settings = default_settings

    factory = ApplicationFactory(settings)
    return factory.create_app()